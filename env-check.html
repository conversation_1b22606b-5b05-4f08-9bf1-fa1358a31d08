<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome扩展环境检查</title>
    <style>
        body {
            width: 400px;
            height: 500px;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
        
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #ddd;
        }
        
        .check-item.success {
            border-left-color: #4caf50;
        }
        
        .check-item.error {
            border-left-color: #f44336;
        }
        
        .check-item.warning {
            border-left-color: #ff9800;
        }
        
        .status-icon {
            font-size: 18px;
            font-weight: bold;
        }
        
        .success .status-icon {
            color: #4caf50;
        }
        
        .error .status-icon {
            color: #f44336;
        }
        
        .warning .status-icon {
            color: #ff9800;
        }
        
        .details {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
        }
        
        button {
            width: 100%;
            padding: 10px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 0;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .summary {
            background: white;
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .summary.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .summary.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🔍 Chrome扩展环境检查</h2>
        <p>诊断AI助手插件的运行环境</p>
    </div>
    
    <div id="checkResults">
        <!-- 检查结果将在这里显示 -->
    </div>
    
    <button onclick="runFullCheck()">重新检查</button>
    
    <div id="summary" class="summary" style="display: none;">
        <!-- 总结将在这里显示 -->
    </div>
    
    <div class="details" id="detailsLog">
        点击"重新检查"开始诊断...
    </div>
    
    <script>
        function log(message) {
            const detailsLog = document.getElementById('detailsLog');
            const timestamp = new Date().toLocaleTimeString();
            detailsLog.textContent += `[${timestamp}] ${message}\n`;
            detailsLog.scrollTop = detailsLog.scrollHeight;
            console.log(message);
        }
        
        function addCheckResult(name, status, details = '') {
            const container = document.getElementById('checkResults');
            const item = document.createElement('div');
            item.className = `check-item ${status}`;
            
            const statusIcon = status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️';
            
            item.innerHTML = `
                <span>${name}</span>
                <span class="status-icon">${statusIcon}</span>
            `;
            
            if (details) {
                const detailsDiv = document.createElement('div');
                detailsDiv.style.fontSize = '11px';
                detailsDiv.style.color = '#666';
                detailsDiv.style.marginTop = '5px';
                detailsDiv.textContent = details;
                item.appendChild(detailsDiv);
            }
            
            container.appendChild(item);
        }
        
        function runFullCheck() {
            log('=== 开始环境检查 ===');
            
            // 清空之前的结果
            document.getElementById('checkResults').innerHTML = '';
            document.getElementById('detailsLog').textContent = '';
            document.getElementById('summary').style.display = 'none';
            
            let successCount = 0;
            let totalChecks = 0;
            
            // 检查1: 基本环境
            totalChecks++;
            log('检查1: 基本环境');
            if (typeof window !== 'undefined' && typeof document !== 'undefined') {
                addCheckResult('基本环境', 'success', 'DOM和Window对象正常');
                successCount++;
                log('✅ 基本环境正常');
            } else {
                addCheckResult('基本环境', 'error', '缺少基本的DOM或Window对象');
                log('❌ 基本环境异常');
            }
            
            // 检查2: Chrome对象
            totalChecks++;
            log('检查2: Chrome对象');
            if (typeof chrome !== 'undefined') {
                addCheckResult('Chrome对象', 'success', 'chrome对象存在');
                successCount++;
                log('✅ chrome对象存在');
            } else {
                addCheckResult('Chrome对象', 'error', 'chrome对象不存在 - 可能不在扩展环境中');
                log('❌ chrome对象不存在');
            }
            
            // 检查3: Chrome Runtime
            totalChecks++;
            log('检查3: Chrome Runtime');
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                const extensionId = chrome.runtime.id || '未知';
                addCheckResult('Chrome Runtime', 'success', `扩展ID: ${extensionId}`);
                successCount++;
                log(`✅ chrome.runtime存在，扩展ID: ${extensionId}`);
            } else {
                addCheckResult('Chrome Runtime', 'error', 'chrome.runtime不可用');
                log('❌ chrome.runtime不可用');
            }
            
            // 检查4: Chrome Storage
            totalChecks++;
            log('检查4: Chrome Storage');
            if (typeof chrome !== 'undefined' && chrome.storage) {
                addCheckResult('Chrome Storage', 'success', 'storage API可用');
                successCount++;
                log('✅ chrome.storage存在');
            } else {
                addCheckResult('Chrome Storage', 'error', 'storage API不可用');
                log('❌ chrome.storage不存在');
            }
            
            // 检查5: Chrome Storage Sync
            totalChecks++;
            log('检查5: Chrome Storage Sync');
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                addCheckResult('Storage Sync', 'success', 'sync存储可用');
                successCount++;
                log('✅ chrome.storage.sync存在');
            } else {
                addCheckResult('Storage Sync', 'error', 'sync存储不可用');
                log('❌ chrome.storage.sync不存在');
            }
            
            // 检查6: 实际存储测试
            totalChecks++;
            log('检查6: 实际存储测试');
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                chrome.storage.sync.set({envTest: Date.now()}, function() {
                    if (chrome.runtime.lastError) {
                        addCheckResult('存储写入测试', 'error', chrome.runtime.lastError.message);
                        log('❌ 存储写入失败: ' + chrome.runtime.lastError.message);
                    } else {
                        addCheckResult('存储写入测试', 'success', '写入测试成功');
                        log('✅ 存储写入测试成功');
                        
                        // 测试读取
                        chrome.storage.sync.get(['envTest'], function(result) {
                            if (chrome.runtime.lastError) {
                                addCheckResult('存储读取测试', 'error', chrome.runtime.lastError.message);
                                log('❌ 存储读取失败: ' + chrome.runtime.lastError.message);
                            } else {
                                addCheckResult('存储读取测试', 'success', '读取测试成功');
                                log('✅ 存储读取测试成功');
                                successCount += 2;
                                showSummary(successCount, totalChecks + 2);
                            }
                        });
                    }
                });
            } else {
                addCheckResult('存储写入测试', 'error', '无法进行存储测试');
                addCheckResult('存储读取测试', 'error', '无法进行存储测试');
                log('❌ 无法进行存储测试');
                totalChecks += 2;
                showSummary(successCount, totalChecks);
            }
            
            // 如果没有异步操作，直接显示总结
            if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.sync) {
                showSummary(successCount, totalChecks);
            }
            
            log('=== 环境检查完成 ===');
        }
        
        function showSummary(successCount, totalChecks) {
            const summary = document.getElementById('summary');
            const percentage = Math.round((successCount / totalChecks) * 100);
            
            if (successCount === totalChecks) {
                summary.className = 'summary success';
                summary.innerHTML = `
                    <h3>🎉 环境检查通过</h3>
                    <p>所有检查项目都通过了 (${successCount}/${totalChecks})</p>
                    <p>Chrome存储API应该可以正常使用</p>
                `;
            } else {
                summary.className = 'summary error';
                summary.innerHTML = `
                    <h3>⚠️ 环境检查发现问题</h3>
                    <p>通过率: ${percentage}% (${successCount}/${totalChecks})</p>
                    <p>请检查插件是否正确安装和启用</p>
                `;
            }
            
            summary.style.display = 'block';
            log(`总结: ${successCount}/${totalChecks} 项检查通过 (${percentage}%)`);
        }
        
        // 页面加载时自动运行检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runFullCheck, 100);
        });
    </script>
</body>
</html>
