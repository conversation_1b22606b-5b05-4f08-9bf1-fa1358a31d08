<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="这是一个用于测试AI网页助手Chrome插件的示例页面">
    <meta name="keywords" content="AI, Chrome插件, 测试页面, 人工智能">
    <title>AI网页助手测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .emoji {
            font-size: 2em;
            margin-right: 10px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI网页助手测试页面</h1>
        
        <div class="highlight">
            <h2>欢迎使用AI网页助手！</h2>
            <p>这是一个专门用于测试Chrome插件功能的示例页面。点击浏览器工具栏中的插件图标，然后点击"分析页面"按钮，看看AI会为这个页面生成什么有趣的内容！</p>
        </div>
        
        <h2>页面内容概述</h2>
        <p>这个测试页面包含了丰富的内容，包括技术介绍、功能特点、使用说明等。AI助手将分析这些内容，并根据你设置的提示词生成相应的弹窗消息。</p>
        
        <div class="feature-list">
            <div class="feature-card">
                <h3><span class="emoji">🎯</span>智能分析</h3>
                <p>AI能够理解页面的主要内容和主题，提取关键信息进行分析。</p>
            </div>
            
            <div class="feature-card">
                <h3><span class="emoji">💬</span>自然对话</h3>
                <p>通过自然语言与AI交互，获得人性化的回应和建议。</p>
            </div>
            
            <div class="feature-card">
                <h3><span class="emoji">🎨</span>个性化</h3>
                <p>支持自定义提示词，让AI按照你的需求生成内容。</p>
            </div>
            
            <div class="feature-card">
                <h3><span class="emoji">⚡</span>快速响应</h3>
                <p>基于现代Web技术，提供流畅的用户体验。</p>
            </div>
        </div>
        
        <h2>技术特点</h2>
        <p>这个Chrome插件使用了以下技术：</p>
        
        <div class="code-block">
// 主要技术栈
- Manifest V3 (最新Chrome扩展标准)
- JavaScript ES6+ (现代JavaScript语法)
- OpenAI GPT API (人工智能模型)
- CSS3 (现代样式和动画)
- Chrome Extension APIs (浏览器集成)
        </div>
        
        <div class="warning">
            <strong>注意：</strong> 使用此插件需要有效的OpenAI API密钥。请确保你已经在OpenAI平台注册并获取了API密钥。
        </div>
        
        <h2>使用步骤</h2>
        <ol>
            <li>在插件弹窗中输入你的OpenAI API密钥</li>
            <li>可选：自定义提示词来控制AI的回应风格</li>
            <li>点击"分析页面"按钮</li>
            <li>等待AI分析完成并显示弹窗</li>
            <li>享受AI为你生成的有趣内容！</li>
        </ol>
        
        <div class="success">
            <strong>提示：</strong> 你可以尝试不同的提示词来获得不同风格的回应。比如要求AI用幽默的方式、专业的角度、或者教育性的方式来分析页面内容。
        </div>
        
        <h2>示例提示词</h2>
        <ul>
            <li><strong>幽默风格：</strong>"请用幽默风趣的方式总结这个页面，并给出一个有趣的评论"</li>
            <li><strong>专业分析：</strong>"请从技术角度分析这个页面的特点和优势"</li>
            <li><strong>学习助手：</strong>"请提取这个页面的关键知识点，并给出学习建议"</li>
            <li><strong>创意思考：</strong>"请发挥创意，为这个页面想一个有趣的标语或口号"</li>
        </ul>
        
        <h2>关于这个项目</h2>
        <p>AI网页助手是一个开源的Chrome插件项目，旨在展示如何将人工智能技术集成到浏览器扩展中。它结合了现代Web开发技术和AI能力，为用户提供智能化的网页浏览体验。</p>
        
        <p>项目的核心理念是让AI成为用户浏览网页时的智能伙伴，能够理解页面内容并提供有价值的见解、建议或娱乐内容。</p>
        
        <div class="highlight">
            <h3>🚀 现在就试试吧！</h3>
            <p>点击浏览器工具栏中的AI网页助手图标，开始你的智能浏览之旅！</p>
        </div>
    </div>
</body>
</html>
