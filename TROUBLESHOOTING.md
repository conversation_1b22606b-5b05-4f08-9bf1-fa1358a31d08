# AI助手故障排除指南

## 🚨 当前问题

用户报告的问题：
1. **无法保存设置** - 点击"保存设置"按钮没有反应
2. **无法进入对话页面** - 设置保存后无法切换到对话界面

## 🔍 诊断步骤

### 1. 基本检查

#### 打开浏览器开发者工具
1. 右键点击插件弹窗
2. 选择"检查"或按F12
3. 查看Console标签页的错误信息

#### 检查插件加载状态
1. 访问 `chrome://extensions/`
2. 确认插件已启用
3. 查看是否有错误提示

### 2. 使用调试工具

#### 方法1: 使用调试页面
1. 打开 `debug-popup.html` 页面
2. 测试Chrome存储功能
3. 查看详细的调试日志

#### 方法2: 使用简化版本
1. 临时将 `popup-simple.html` 重命名为 `popup.html`
2. 将 `popup-simple.js` 重命名为 `popup.js`
3. 重新加载插件测试基本功能

### 3. 常见问题排查

#### 问题1: Chrome存储权限
**症状**: 保存设置时没有反应或报错
**解决方案**:
```javascript
// 在控制台中测试
chrome.storage.sync.set({test: 'hello'}, function() {
  if (chrome.runtime.lastError) {
    console.error('存储失败:', chrome.runtime.lastError);
  } else {
    console.log('存储成功');
  }
});
```

#### 问题2: 元素未找到
**症状**: 控制台显示元素为null或undefined
**解决方案**:
1. 检查HTML中的元素ID是否正确
2. 确认JavaScript在DOM加载完成后执行
3. 检查CSS是否隐藏了元素

#### 问题3: 事件监听器未绑定
**症状**: 点击按钮没有反应
**解决方案**:
```javascript
// 在控制台中测试
const btn = document.getElementById('saveSettingsBtn');
console.log('按钮元素:', btn);
if (btn) {
  btn.addEventListener('click', () => console.log('按钮被点击'));
}
```

#### 问题4: CSP (内容安全策略) 限制
**症状**: 外部脚本无法加载
**解决方案**:
1. 检查manifest.json中的权限设置
2. 使用本地文件替代CDN资源
3. 检查是否有内联脚本被阻止

## 🛠️ 修复方案

### 临时解决方案

#### 1. 使用简化版本
如果主版本有问题，可以临时使用简化版本：

```bash
# 备份原文件
mv popup.html popup-full.html
mv popup.js popup-full.js

# 使用简化版本
cp popup-simple.html popup.html
cp popup-simple.js popup.js

# 重新加载插件
```

#### 2. 手动测试存储
在控制台中手动测试存储功能：

```javascript
// 保存测试数据
chrome.storage.sync.set({
  baseUrl: 'https://api.openai.com/v1/chat/completions',
  modelName: 'gpt-3.5-turbo',
  apiKey: 'your-api-key',
  systemPrompt: 'test prompt'
}, function() {
  console.log('保存完成');
});

// 读取测试数据
chrome.storage.sync.get(['baseUrl', 'modelName', 'apiKey', 'systemPrompt'], function(result) {
  console.log('读取结果:', result);
});
```

### 永久解决方案

#### 1. 代码优化
- 添加更多错误处理
- 改进元素检查逻辑
- 增强调试信息

#### 2. 权限检查
确保manifest.json包含必要权限：
```json
{
  "permissions": [
    "storage",
    "activeTab",
    "scripting"
  ]
}
```

#### 3. 兼容性改进
- 检查Chrome版本兼容性
- 添加降级方案
- 改进错误提示

## 📋 调试清单

### 基本检查
- [ ] 插件已正确加载
- [ ] 开发者工具无错误信息
- [ ] 所有文件都存在且可访问
- [ ] manifest.json语法正确

### 功能检查
- [ ] 元素ID匹配
- [ ] 事件监听器正确绑定
- [ ] Chrome存储API可用
- [ ] 页面切换功能正常

### 高级检查
- [ ] CSP策略不冲突
- [ ] 异步操作正确处理
- [ ] 错误处理完善
- [ ] 用户体验流畅

## 🆘 获取帮助

如果以上方法都无法解决问题：

1. **收集信息**:
   - Chrome版本
   - 操作系统
   - 控制台错误信息
   - 复现步骤

2. **使用调试工具**:
   - 打开 `debug-popup.html`
   - 运行完整测试
   - 记录所有输出

3. **提供反馈**:
   - 详细描述问题
   - 附上调试信息
   - 说明期望行为

## 🔄 版本回退

如果新版本有问题，可以回退到稳定版本：

1. 备份当前版本
2. 恢复之前的工作版本
3. 逐步应用新功能
4. 测试每个变更

记住：**稳定性比功能更重要**！
