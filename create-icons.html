<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>创建插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .icon-container {
            display: inline-block;
            margin: 20px;
            text-align: center;
        }
        
        .icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-bottom: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .icon-16 {
            width: 16px;
            height: 16px;
            font-size: 8px;
        }
        
        .icon-48 {
            width: 48px;
            height: 48px;
            font-size: 24px;
        }
        
        .icon-128 {
            width: 128px;
            height: 128px;
            font-size: 64px;
        }
        
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .robot-icon {
            position: relative;
        }
        
        .robot-icon::before {
            content: '🤖';
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>创建Chrome插件图标</h2>
        <p>请按照以下步骤创建图标文件：</p>
        <ol>
            <li>右键点击下面的每个图标</li>
            <li>选择"检查元素"或"审查元素"</li>
            <li>在开发者工具中右键点击对应的div元素</li>
            <li>选择"截图节点"或使用截图工具精确截取图标</li>
            <li>将截图保存为PNG格式，文件名分别为：
                <ul>
                    <li>icon16.png (16x16像素)</li>
                    <li>icon48.png (48x48像素)</li>
                    <li>icon128.png (128x128像素)</li>
                </ul>
            </li>
            <li>将这些文件放入icons文件夹中</li>
        </ol>
        <p><strong>或者</strong>，你可以使用任何图像编辑软件创建这些尺寸的PNG图标文件。</p>
    </div>
    
    <h3>图标预览：</h3>
    
    <div class="icon-container">
        <div class="icon icon-16 robot-icon"></div>
        <div>16x16</div>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-48 robot-icon"></div>
        <div>48x48</div>
    </div>
    
    <div class="icon-container">
        <div class="icon icon-128 robot-icon"></div>
        <div>128x128</div>
    </div>
    
    <div class="instructions">
        <h3>快速解决方案</h3>
        <p>如果你想快速测试插件而不创建图标，可以：</p>
        <ol>
            <li>保持manifest.json中没有icons部分（我已经移除了）</li>
            <li>Chrome会使用默认图标</li>
            <li>插件功能完全正常，只是没有自定义图标</li>
        </ol>
        
        <h3>在线图标生成器</h3>
        <p>你也可以使用在线工具生成图标：</p>
        <ul>
            <li><a href="https://www.favicon-generator.org/" target="_blank">Favicon Generator</a></li>
            <li><a href="https://realfavicongenerator.net/" target="_blank">Real Favicon Generator</a></li>
            <li><a href="https://www.canva.com/" target="_blank">Canva</a> - 创建自定义图标</li>
        </ul>
    </div>
</body>
</html>
