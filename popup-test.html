<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI助手测试</title>
  <style>
    body {
      width: 350px;
      height: 400px;
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: #f5f7fa;
    }
    
    .header {
      background: #667eea;
      color: white;
      padding: 15px;
      margin: -20px -20px 20px -20px;
      text-align: center;
    }
    
    .test-section {
      background: white;
      padding: 15px;
      margin: 10px 0;
      border-radius: 8px;
      border: 1px solid #ddd;
    }
    
    input, button {
      width: 100%;
      padding: 10px;
      margin: 5px 0;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    button {
      background: #667eea;
      color: white;
      border: none;
      cursor: pointer;
    }
    
    button:hover {
      background: #5a6fd8;
    }
    
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      text-align: center;
      display: none;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
    }
    
    .log {
      background: #f8f9fa;
      border: 1px solid #ddd;
      padding: 10px;
      height: 100px;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="header">
    <h2>🧪 AI助手功能测试</h2>
  </div>
  
  <div class="test-section">
    <h3>环境检查</h3>
    <button onclick="checkEnvironment()">检查Chrome扩展环境</button>
    <div id="envStatus" class="status"></div>
  </div>
  
  <div class="test-section">
    <h3>存储测试</h3>
    <input type="text" id="testKey" placeholder="测试键" value="testKey">
    <input type="text" id="testValue" placeholder="测试值" value="Hello World">
    <button onclick="testStorage()">测试存储功能</button>
    <div id="storageStatus" class="status"></div>
  </div>
  
  <div class="test-section">
    <h3>调试日志</h3>
    <div id="debugLog" class="log">等待测试...</div>
    <button onclick="clearLog()">清空日志</button>
  </div>
  
  <script>
    function log(message) {
      const logDiv = document.getElementById('debugLog');
      const timestamp = new Date().toLocaleTimeString();
      logDiv.textContent += `[${timestamp}] ${message}\n`;
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }
    
    function showStatus(elementId, message, type) {
      const statusDiv = document.getElementById(elementId);
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      statusDiv.style.display = 'block';
      
      setTimeout(() => {
        statusDiv.style.display = 'none';
      }, 3000);
    }
    
    function checkEnvironment() {
      log('=== 开始环境检查 ===');
      
      let issues = [];
      let success = 0;
      let total = 5;
      
      // 检查Chrome对象
      if (typeof chrome === 'undefined') {
        issues.push('Chrome对象不存在');
        log('❌ Chrome对象不存在');
      } else {
        success++;
        log('✅ Chrome对象存在');
      }
      
      // 检查Runtime
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        issues.push('Chrome Runtime不可用');
        log('❌ Chrome Runtime不可用');
      } else {
        success++;
        log('✅ Chrome Runtime可用');
        log(`   扩展ID: ${chrome.runtime.id}`);
      }
      
      // 检查Storage
      if (typeof chrome === 'undefined' || !chrome.storage) {
        issues.push('Chrome Storage不可用');
        log('❌ Chrome Storage不可用');
      } else {
        success++;
        log('✅ Chrome Storage可用');
      }
      
      // 检查Storage Sync
      if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.sync) {
        issues.push('Chrome Storage Sync不可用');
        log('❌ Chrome Storage Sync不可用');
      } else {
        success++;
        log('✅ Chrome Storage Sync可用');
      }
      
      // 检查Manifest
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest) {
        const manifest = chrome.runtime.getManifest();
        success++;
        log('✅ Manifest可访问');
        log(`   版本: ${manifest.version}`);
        log(`   权限: ${manifest.permissions.join(', ')}`);
      } else {
        issues.push('无法访问Manifest');
        log('❌ 无法访问Manifest');
      }
      
      const percentage = Math.round((success / total) * 100);
      log(`=== 检查完成: ${success}/${total} (${percentage}%) ===`);
      
      if (issues.length === 0) {
        showStatus('envStatus', '环境检查通过！', 'success');
      } else {
        showStatus('envStatus', `发现 ${issues.length} 个问题`, 'error');
      }
    }
    
    function testStorage() {
      log('=== 开始存储测试 ===');
      
      const key = document.getElementById('testKey').value;
      const value = document.getElementById('testValue').value;
      
      if (!key || !value) {
        showStatus('storageStatus', '请输入测试键和值', 'error');
        return;
      }
      
      if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.sync) {
        log('❌ Chrome Storage不可用');
        showStatus('storageStatus', 'Chrome Storage不可用', 'error');
        return;
      }
      
      // 测试写入
      const testData = {};
      testData[key] = value;
      
      chrome.storage.sync.set(testData, function() {
        if (chrome.runtime.lastError) {
          log('❌ 存储写入失败: ' + chrome.runtime.lastError.message);
          showStatus('storageStatus', '存储写入失败', 'error');
        } else {
          log('✅ 存储写入成功');
          
          // 测试读取
          chrome.storage.sync.get([key], function(result) {
            if (chrome.runtime.lastError) {
              log('❌ 存储读取失败: ' + chrome.runtime.lastError.message);
              showStatus('storageStatus', '存储读取失败', 'error');
            } else {
              log('✅ 存储读取成功');
              log(`   读取值: ${result[key]}`);
              
              if (result[key] === value) {
                log('✅ 数据一致性验证通过');
                showStatus('storageStatus', '存储测试完全成功！', 'success');
              } else {
                log('❌ 数据一致性验证失败');
                showStatus('storageStatus', '数据不一致', 'error');
              }
            }
          });
        }
      });
      
      log('=== 存储测试完成 ===');
    }
    
    function clearLog() {
      document.getElementById('debugLog').textContent = '';
    }
    
    // 页面加载时自动检查环境
    document.addEventListener('DOMContentLoaded', function() {
      log('测试页面已加载');
      setTimeout(checkEnvironment, 100);
    });
  </script>
</body>
</html>
