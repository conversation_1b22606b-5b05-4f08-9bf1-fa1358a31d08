<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天记录持久化测试页面</title>
    <meta name="description" content="测试AI网页助手的聊天记录持久化功能">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 4px solid #667eea;
        }
        
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
            text-align: center;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .feature-card {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4caf50;
        }
        
        .feature-card h4 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .test-steps {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
        }
        
        .test-steps h3 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .test-steps ol {
            margin: 15px 0;
            padding-left: 20px;
        }
        
        .test-steps li {
            margin: 10px 0;
            font-weight: 500;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💾💬 聊天记录持久化测试</h1>
            <p>测试AI助手的聊天记录保存和恢复功能</p>
        </div>
        
        <div class="highlight-box">
            <h2>🎯 测试目标</h2>
            <p>验证聊天记录能够在关闭插件后重新打开时正确恢复，确保用户的对话历史不会丢失。</p>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>💾 自动保存</h4>
                <p>每条消息都会自动保存到Chrome本地存储，无需手动操作。</p>
            </div>
            
            <div class="feature-card">
                <h4>🔄 自动恢复</h4>
                <p>重新打开插件时，会自动加载该页面的历史聊天记录。</p>
            </div>
            
            <div class="feature-card">
                <h4>📄 按页面分组</h4>
                <p>不同页面的聊天记录分别保存，互不干扰。</p>
            </div>
            
            <div class="feature-card">
                <h4>🗑️ 智能清理</h4>
                <p>自动清理30天前的旧记录，保持存储空间整洁。</p>
            </div>
        </div>
        
        <div class="test-steps">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>初始测试</strong>
                    <ul>
                        <li>打开AI助手插件</li>
                        <li>配置API设置</li>
                        <li>切换到对话页面</li>
                        <li>发送几条测试消息</li>
                    </ul>
                </li>
                
                <li><strong>持久化测试</strong>
                    <ul>
                        <li>关闭插件弹窗</li>
                        <li>重新点击插件图标</li>
                        <li>检查聊天记录是否恢复</li>
                    </ul>
                </li>
                
                <li><strong>页面切换测试</strong>
                    <ul>
                        <li>切换到另一个网页</li>
                        <li>打开插件，应该看到新的对话</li>
                        <li>切换回本页面</li>
                        <li>检查原有聊天记录是否保留</li>
                    </ul>
                </li>
                
                <li><strong>功能测试</strong>
                    <ul>
                        <li>点击"聊天统计"查看统计信息</li>
                        <li>测试"清空对话"功能（会有确认提示）</li>
                        <li>测试"刷新页面信息"功能</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🔧 技术实现</h3>
            <p>聊天记录持久化使用以下技术：</p>
            <div class="code-example">
// 保存聊天记录
chrome.storage.local.set({
  [`chatHistory_${pageKey}`]: {
    url: currentPageUrl,
    history: chatHistory,
    timestamp: Date.now(),
    pageTitle: pageContext?.title
  }
});

// 加载聊天记录
chrome.storage.local.get([storageKey], (result) => {
  const chatData = result[storageKey];
  if (chatData && chatData.history) {
    chatHistory = chatData.history;
    renderChatHistory();
  }
});
            </div>
        </div>
        
        <div class="info">
            <strong>💡 存储说明：</strong>
            <ul>
                <li>聊天记录保存在Chrome的本地存储中</li>
                <li>每个页面的记录使用URL作为唯一标识</li>
                <li>记录包含消息内容、时间戳、用户类型等信息</li>
                <li>自动清理30天前的旧记录</li>
            </ul>
        </div>
        
        <div class="success">
            <strong>✅ 预期结果：</strong>
            <ul>
                <li>聊天记录在关闭插件后重新打开时能够正确恢复</li>
                <li>不同页面的聊天记录相互独立</li>
                <li>清空对话时会显示确认提示</li>
                <li>聊天统计能够显示正确的消息数量和时间信息</li>
            </ul>
        </div>
        
        <div class="warning">
            <strong>⚠️ 注意事项：</strong>
            <ul>
                <li>聊天记录保存在本地，不会同步到其他设备</li>
                <li>清除浏览器数据会删除所有聊天记录</li>
                <li>卸载插件会丢失所有聊天记录</li>
                <li>隐私模式下的聊天记录不会保存</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🎯 测试要点</h3>
            <ul>
                <li><strong>消息完整性</strong>: 所有消息内容和格式都应正确恢复</li>
                <li><strong>时间顺序</strong>: 消息的时间顺序应该保持正确</li>
                <li><strong>Markdown渲染</strong>: AI回复的Markdown格式应该正确显示</li>
                <li><strong>页面隔离</strong>: 不同页面的聊天记录应该相互独立</li>
                <li><strong>用户体验</strong>: 加载过程应该流畅，无明显延迟</li>
            </ul>
        </div>
        
        <div class="highlight-box">
            <h3>🚀 开始测试</h3>
            <p>现在点击浏览器工具栏中的AI助手图标，开始测试聊天记录持久化功能！</p>
            <p>建议先发送几条包含不同格式的消息，然后关闭重开插件验证恢复效果。</p>
        </div>
    </div>
</body>
</html>
