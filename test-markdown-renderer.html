<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown渲染器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-input {
            width: 100%;
            height: 200px;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
        }
        .test-output {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background: #f9f9f9;
            min-height: 200px;
        }
        button {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        button:hover {
            background: #005a87;
        }
        
        /* Markdown样式 */
        .test-output h1, .test-output h2, .test-output h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .test-output code {
            background: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        .test-output pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .test-output pre code {
            background: none;
            padding: 0;
        }
        .test-output blockquote {
            border-left: 4px solid #ddd;
            margin: 0;
            padding-left: 15px;
            color: #666;
        }
        .test-output table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        .test-output th, .test-output td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .test-output th {
            background: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Markdown渲染器测试</h1>
    
    <textarea class="test-input" id="markdownInput" placeholder="在这里输入Markdown文本...">
# 标题测试

## 二级标题

### 三级标题

这是一个段落，包含 **粗体文本** 和 *斜体文本*。

还有 `内联代码` 和 [链接](https://example.com)。

## 列表测试

### 无序列表
- 项目1
- 项目2
- 项目3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 代码块测试

```javascript
function hello(name) {
    return `Hello, ${name}!`;
}

console.log(hello('World'));
```

## 表格测试

| 功能 | 状态 | 说明 |
|------|------|------|
| 标题 | ✅ | 支持H1-H6 |
| 列表 | ✅ | 有序和无序 |
| 代码 | ✅ | 内联和块级 |

## 引用测试

> 这是一个引用块的示例。
> 可以包含多行内容。

## 其他格式

~~删除线文本~~

**粗体** 和 *斜体* 的组合。
    </textarea>
    
    <button onclick="testRender()">渲染测试</button>
    
    <div class="test-output" id="output"></div>
    
    <script src="markdown-renderer.js"></script>
    <script>
        function testRender() {
            const input = document.getElementById('markdownInput').value;
            const output = document.getElementById('output');
            
            if (typeof window.markdownRenderer !== 'undefined') {
                try {
                    const rendered = window.markdownRenderer.render(input);
                    output.innerHTML = rendered;
                    
                    // 应用代码高亮
                    if (typeof window.hljs !== 'undefined') {
                        output.querySelectorAll('pre code').forEach((block) => {
                            window.hljs.highlightElement(block);
                        });
                    }
                    
                    console.log('渲染成功');
                } catch (error) {
                    output.innerHTML = `<p style="color: red;">渲染错误: ${error.message}</p>`;
                    console.error('渲染失败:', error);
                }
            } else {
                output.innerHTML = '<p style="color: red;">Markdown渲染器未加载</p>';
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', function() {
            setTimeout(testRender, 100);
        });
    </script>
</body>
</html>
