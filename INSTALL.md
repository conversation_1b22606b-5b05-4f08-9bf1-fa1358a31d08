# 安装和使用指南

## 快速开始

### 1. 安装插件

1. **下载项目文件**
   - 确保所有文件都在 `chrome-assistant` 文件夹中

2. **打开Chrome扩展管理页面**
   - 在Chrome浏览器地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在页面右上角打开"开发者模式"开关

4. **加载插件**
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-assistant` 文件夹
   - 插件应该会出现在扩展列表中

### 2. 获取API密钥

1. **访问OpenAI官网**
   - 打开 https://platform.openai.com/
   - 注册账号或登录

2. **创建API密钥**
   - 进入 API Keys 页面
   - 点击 "Create new secret key"
   - 复制生成的密钥（以 `sk-` 开头）

3. **配置插件**
   - 点击Chrome工具栏中的插件图标
   - 在"设置"页面配置以下信息：
     - **API Base URL**: 默认为 `https://api.openai.com/v1/chat/completions`
     - **模型名称**: 默认为 `gpt-3.5-turbo`
     - **API密钥**: 粘贴你的OpenAI API密钥
     - **系统提示词**: 设置AI的角色和行为方式
   - 点击"保存设置"按钮
   - 设置保存后会自动切换到对话页面

### 3. 测试插件

1. **打开测试页面**
   - 在浏览器中打开 `markdown-test.html`（推荐，测试Markdown功能）
   - 或者使用 `chat-test.html`、`sse-markdown-test.html`
   - 也可以使用 `test-page.html`、`debug-test.html`
   - 也可以访问任何其他网页

2. **开始对话**
   - 点击插件图标打开弹窗
   - 如果已配置，会直接显示对话页面
   - 在输入框中输入你的问题
   - 按Enter或点击发送按钮
   - AI会基于页面内容回答你的问题

3. **对话功能**
   - 支持多轮连续对话
   - AI会记住对话历史
   - 可以随时清空对话重新开始
   - 可以刷新页面信息获取最新内容

## 高级配置

### API服务配置

插件现在支持配置不同的AI服务提供商：

**OpenAI 标准配置：**
- Base URL: `https://api.openai.com/v1/chat/completions`
- 模型: `gpt-3.5-turbo`, `gpt-4`, `gpt-4-turbo`

**Azure OpenAI 配置：**
- Base URL: `https://your-resource.openai.azure.com/openai/deployments/your-deployment/chat/completions?api-version=2023-05-15`
- 模型: `gpt-35-turbo` (注意Azure中的命名格式)

**其他兼容服务：**
- 根据服务提供商的API文档配置相应的URL和模型名称

### 自定义提示词

在插件弹窗中，你可以修改"自定义提示词"来控制AI的回应风格：

**示例提示词：**

```
幽默风格：
"请用幽默风趣的方式总结这个页面，并给出一个搞笑的评论"

专业分析：
"请从专业角度分析这个页面的内容质量和用户体验"

学习助手：
"请提取这个页面的关键知识点，并给出3个学习要点"

创意思考：
"请为这个页面想一个有创意的标语，并解释为什么"
```

### API使用优化

1. **控制成本**
   - 页面内容会被截取到2000字符以内
   - 可以调整提示词长度来控制API调用成本

2. **提高效果**
   - 使用具体、清晰的提示词
   - 可以指定回应的长度和风格
   - 尝试不同的提示词来获得最佳效果

## 故障排除

### 常见问题

**1. 插件无法加载**
```
解决方案：
- 检查所有文件是否在正确位置
- 确认manifest.json语法正确
- 重新加载插件
```

**2. "无法连接到页面" 错误**
```
可能原因：
- 在不支持的页面使用插件（如chrome://页面）
- Content Script未正确加载
- 页面权限限制

解决方案：
- 确保不在chrome://、edge://等特殊页面使用
- 刷新页面后重试
- 使用debug-test.html页面进行测试
- 检查浏览器控制台错误信息
- 重新加载插件
```

**3. API调用失败**
```
可能原因：
- API密钥无效或过期
- Base URL配置错误
- 模型名称不正确
- 网络连接问题
- OpenAI服务暂时不可用
- 账户余额不足

解决方案：
- 验证API密钥格式（应以sk-开头）
- 检查Base URL是否正确
- 确认模型名称可用
- 检查OpenAI账户状态
- 确认网络连接正常
```

**3. 弹窗不显示**
```
可能原因：
- 页面内容安全策略限制
- JavaScript被禁用
- 其他扩展冲突

解决方案：
- 检查浏览器控制台错误信息
- 暂时禁用其他扩展进行测试
- 刷新页面后重试
```

**4. 分析结果不理想**
```
解决方案：
- 调整自定义提示词
- 尝试更具体的指令
- 在不同类型的页面上测试
```

### 调试方法

1. **查看控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **检查网络请求**
   - 在Network标签页查看API请求状态
   - 确认请求是否成功发送

3. **验证插件状态**
   - 在 `chrome://extensions/` 页面查看插件状态
   - 点击"详细信息"查看权限设置

## 开发和定制

### 修改插件

1. **编辑代码**
   - 修改相应的JavaScript、HTML或CSS文件
   - 保存更改

2. **重新加载插件**
   - 在扩展管理页面点击刷新按钮
   - 或者移除后重新加载插件

3. **测试更改**
   - 在测试页面验证功能
   - 检查控制台是否有错误

### 添加新功能

插件采用模块化设计，你可以轻松添加新功能：

- **popup.js**: 修改弹窗界面逻辑
- **content.js**: 添加页面交互功能
- **background.js**: 实现后台处理逻辑
- **content.css**: 自定义弹窗样式

## 安全注意事项

1. **API密钥安全**
   - 不要在公共场所输入API密钥
   - 定期更换API密钥
   - 监控API使用量

2. **隐私保护**
   - 插件只分析当前页面内容
   - 不会存储或上传个人数据
   - API密钥仅存储在本地

3. **权限管理**
   - 插件只请求必要的权限
   - 可以随时在扩展管理页面禁用插件

## 支持和反馈

如果遇到问题或有改进建议，请：

1. 检查本文档的故障排除部分
2. 查看项目的GitHub Issues
3. 提交新的Issue描述问题

祝你使用愉快！🚀
