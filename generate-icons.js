// 简单的图标生成脚本
// 需要安装 canvas 包: npm install canvas

const fs = require('fs');
const path = require('path');

// 检查是否有canvas包
try {
  const { createCanvas } = require('canvas');
  
  function createIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    
    // 绘制圆角矩形背景
    const radius = size * 0.2;
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.roundRect(0, 0, size, size, radius);
    ctx.fill();
    
    // 绘制机器人图标
    ctx.fillStyle = 'white';
    ctx.font = `${size * 0.6}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('🤖', size / 2, size / 2);
    
    return canvas.toBuffer('image/png');
  }
  
  // 创建图标文件夹
  const iconsDir = path.join(__dirname, 'icons');
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
  }
  
  // 生成不同尺寸的图标
  const sizes = [16, 48, 128];
  
  sizes.forEach(size => {
    const buffer = createIcon(size);
    const filename = path.join(iconsDir, `icon${size}.png`);
    fs.writeFileSync(filename, buffer);
    console.log(`Created ${filename}`);
  });
  
  console.log('所有图标已生成完成！');
  
} catch (error) {
  console.log('Canvas包未安装，请使用以下方法之一创建图标：');
  console.log('');
  console.log('方法1: 安装canvas包并运行此脚本');
  console.log('  npm install canvas');
  console.log('  node generate-icons.js');
  console.log('');
  console.log('方法2: 手动创建图标文件');
  console.log('  - 打开 create-icons.html 文件');
  console.log('  - 按照说明截图保存为PNG文件');
  console.log('');
  console.log('方法3: 使用在线图标生成器');
  console.log('  - 访问 https://www.favicon-generator.org/');
  console.log('  - 上传一个图片并生成不同尺寸的图标');
  console.log('');
  console.log('方法4: 暂时不使用图标');
  console.log('  - 插件可以正常工作，Chrome会使用默认图标');
}
