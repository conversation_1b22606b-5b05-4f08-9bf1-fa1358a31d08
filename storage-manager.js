/**
 * 增强的存储管理器
 * 提供多种存储方案的回退机制，解决Chrome storage不可用的问题
 */
class StorageManager {
  constructor() {
    this.storageType = null;
    this.isInitialized = false;
  }

  /**
   * 初始化存储管理器，检测可用的存储方案
   */
  async init() {
    if (this.isInitialized) {
      return this.storageType;
    }

    console.log('🔧 初始化存储管理器...');

    // 方案1: 尝试Chrome Storage Sync
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
        await this.testChromeStorageSync();
        this.storageType = 'chrome-sync';
        console.log('✅ 使用 Chrome Storage Sync');
        this.isInitialized = true;
        return this.storageType;
      }
    } catch (error) {
      console.warn('⚠️ Chrome Storage Sync 不可用:', error.message);
    }

    // 方案2: 尝试Chrome Storage Local
    try {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        await this.testChromeStorageLocal();
        this.storageType = 'chrome-local';
        console.log('✅ 使用 Chrome Storage Local');
        this.isInitialized = true;
        return this.storageType;
      }
    } catch (error) {
      console.warn('⚠️ Chrome Storage Local 不可用:', error.message);
    }

    // 方案3: 回退到LocalStorage
    try {
      if (typeof localStorage !== 'undefined') {
        this.testLocalStorage();
        this.storageType = 'localStorage';
        console.log('✅ 使用 LocalStorage');
        this.isInitialized = true;
        return this.storageType;
      }
    } catch (error) {
      console.warn('⚠️ LocalStorage 不可用:', error.message);
    }

    // 方案4: 内存存储（临时方案）
    this.storageType = 'memory';
    this.memoryStorage = {};
    console.log('⚠️ 使用内存存储（临时方案）');
    this.isInitialized = true;
    return this.storageType;
  }

  /**
   * 测试Chrome Storage Sync
   */
  async testChromeStorageSync() {
    return new Promise((resolve, reject) => {
      const testKey = 'storage_test_' + Date.now();
      const testValue = 'test_value';

      chrome.storage.sync.set({ [testKey]: testValue }, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        chrome.storage.sync.get([testKey], (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (result[testKey] === testValue) {
            // 清理测试数据
            chrome.storage.sync.remove([testKey]);
            resolve();
          } else {
            reject(new Error('数据读写不一致'));
          }
        });
      });
    });
  }

  /**
   * 测试Chrome Storage Local
   */
  async testChromeStorageLocal() {
    return new Promise((resolve, reject) => {
      const testKey = 'storage_test_' + Date.now();
      const testValue = 'test_value';

      chrome.storage.local.set({ [testKey]: testValue }, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        chrome.storage.local.get([testKey], (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (result[testKey] === testValue) {
            // 清理测试数据
            chrome.storage.local.remove([testKey]);
            resolve();
          } else {
            reject(new Error('数据读写不一致'));
          }
        });
      });
    });
  }

  /**
   * 测试LocalStorage
   */
  testLocalStorage() {
    const testKey = 'storage_test_' + Date.now();
    const testValue = 'test_value';
    
    localStorage.setItem(testKey, testValue);
    const retrieved = localStorage.getItem(testKey);
    localStorage.removeItem(testKey);
    
    if (retrieved !== testValue) {
      throw new Error('LocalStorage 读写不一致');
    }
  }

  /**
   * 保存设置
   */
  async saveSettings(settings) {
    await this.init();

    console.log('💾 保存设置，使用存储方案:', this.storageType);

    switch (this.storageType) {
      case 'chrome-sync':
        return this.saveToChromeSync(settings);
      
      case 'chrome-local':
        return this.saveToChromeLocal(settings);
      
      case 'localStorage':
        return this.saveToLocalStorage(settings);
      
      case 'memory':
        return this.saveToMemory(settings);
      
      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * 加载设置
   */
  async loadSettings() {
    await this.init();

    console.log('📥 加载设置，使用存储方案:', this.storageType);

    switch (this.storageType) {
      case 'chrome-sync':
        return this.loadFromChromeSync();
      
      case 'chrome-local':
        return this.loadFromChromeLocal();
      
      case 'localStorage':
        return this.loadFromLocalStorage();
      
      case 'memory':
        return this.loadFromMemory();
      
      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * Chrome Storage Sync 保存
   */
  async saveToChromeSync(settings) {
    return new Promise((resolve, reject) => {
      chrome.storage.sync.set(settings, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Chrome Storage Sync 加载
   */
  async loadFromChromeSync() {
    return new Promise((resolve, reject) => {
      const keys = ['baseUrl', 'modelName', 'apiKey', 'systemPrompt'];
      chrome.storage.sync.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  /**
   * Chrome Storage Local 保存
   */
  async saveToChromeLocal(settings) {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set(settings, () => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Chrome Storage Local 加载
   */
  async loadFromChromeLocal() {
    return new Promise((resolve, reject) => {
      const keys = ['baseUrl', 'modelName', 'apiKey', 'systemPrompt'];
      chrome.storage.local.get(keys, (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(result);
        }
      });
    });
  }

  /**
   * LocalStorage 保存
   */
  async saveToLocalStorage(settings) {
    try {
      const data = JSON.stringify(settings);
      localStorage.setItem('ai_assistant_settings', data);
    } catch (error) {
      throw new Error('LocalStorage 保存失败: ' + error.message);
    }
  }

  /**
   * LocalStorage 加载
   */
  async loadFromLocalStorage() {
    try {
      const data = localStorage.getItem('ai_assistant_settings');
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.warn('LocalStorage 加载失败:', error);
      return {};
    }
  }

  /**
   * 内存存储保存
   */
  async saveToMemory(settings) {
    this.memoryStorage = { ...this.memoryStorage, ...settings };
  }

  /**
   * 内存存储加载
   */
  async loadFromMemory() {
    return { ...this.memoryStorage };
  }

  /**
   * 保存聊天记录
   */
  async saveChatHistory(storageKey, chatData) {
    await this.init();

    switch (this.storageType) {
      case 'chrome-sync':
      case 'chrome-local':
        return this.saveChatToChromeLocal(storageKey, chatData);

      case 'localStorage':
        return this.saveChatToLocalStorage(storageKey, chatData);

      case 'memory':
        return this.saveChatToMemory(storageKey, chatData);

      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * 加载聊天记录
   */
  async loadChatHistory(storageKey) {
    await this.init();

    switch (this.storageType) {
      case 'chrome-sync':
      case 'chrome-local':
        return this.loadChatFromChromeLocal(storageKey);

      case 'localStorage':
        return this.loadChatFromLocalStorage(storageKey);

      case 'memory':
        return this.loadChatFromMemory(storageKey);

      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * 删除聊天记录
   */
  async removeChatHistory(storageKey) {
    await this.init();

    switch (this.storageType) {
      case 'chrome-sync':
      case 'chrome-local':
        return this.removeChatFromChromeLocal(storageKey);

      case 'localStorage':
        return this.removeChatFromLocalStorage(storageKey);

      case 'memory':
        return this.removeChatFromMemory(storageKey);

      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * Chrome Local 聊天记录保存
   */
  async saveChatToChromeLocal(storageKey, chatData) {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        chrome.storage.local.set({ [storageKey]: chatData }, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } else {
        reject(new Error('Chrome Storage Local 不可用'));
      }
    });
  }

  /**
   * Chrome Local 聊天记录加载
   */
  async loadChatFromChromeLocal(storageKey) {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        chrome.storage.local.get([storageKey], (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(result[storageKey] || null);
          }
        });
      } else {
        reject(new Error('Chrome Storage Local 不可用'));
      }
    });
  }

  /**
   * Chrome Local 聊天记录删除
   */
  async removeChatFromChromeLocal(storageKey) {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        chrome.storage.local.remove([storageKey], () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } else {
        reject(new Error('Chrome Storage Local 不可用'));
      }
    });
  }

  /**
   * LocalStorage 聊天记录保存
   */
  async saveChatToLocalStorage(storageKey, chatData) {
    try {
      const data = JSON.stringify(chatData);
      localStorage.setItem(storageKey, data);
    } catch (error) {
      throw new Error('LocalStorage 保存聊天记录失败: ' + error.message);
    }
  }

  /**
   * LocalStorage 聊天记录加载
   */
  async loadChatFromLocalStorage(storageKey) {
    try {
      const data = localStorage.getItem(storageKey);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('LocalStorage 加载聊天记录失败:', error);
      return null;
    }
  }

  /**
   * LocalStorage 聊天记录删除
   */
  async removeChatFromLocalStorage(storageKey) {
    try {
      localStorage.removeItem(storageKey);
    } catch (error) {
      throw new Error('LocalStorage 删除聊天记录失败: ' + error.message);
    }
  }

  /**
   * 内存存储聊天记录保存
   */
  async saveChatToMemory(storageKey, chatData) {
    if (!this.memoryChatStorage) {
      this.memoryChatStorage = {};
    }
    this.memoryChatStorage[storageKey] = chatData;
  }

  /**
   * 内存存储聊天记录加载
   */
  async loadChatFromMemory(storageKey) {
    if (!this.memoryChatStorage) {
      this.memoryChatStorage = {};
    }
    return this.memoryChatStorage[storageKey] || null;
  }

  /**
   * 内存存储聊天记录删除
   */
  async removeChatFromMemory(storageKey) {
    if (!this.memoryChatStorage) {
      this.memoryChatStorage = {};
    }
    delete this.memoryChatStorage[storageKey];
  }

  /**
   * 获取所有聊天记录（用于清理）
   */
  async getAllChatHistory() {
    await this.init();

    switch (this.storageType) {
      case 'chrome-sync':
      case 'chrome-local':
        return this.getAllChatFromChromeLocal();

      case 'localStorage':
        return this.getAllChatFromLocalStorage();

      case 'memory':
        return this.getAllChatFromMemory();

      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * Chrome Local 获取所有聊天记录
   */
  async getAllChatFromChromeLocal() {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        chrome.storage.local.get(null, (allData) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            const chatData = {};
            Object.keys(allData).forEach(key => {
              if (key.startsWith('chatHistory_')) {
                chatData[key] = allData[key];
              }
            });
            resolve(chatData);
          }
        });
      } else {
        reject(new Error('Chrome Storage Local 不可用'));
      }
    });
  }

  /**
   * LocalStorage 获取所有聊天记录
   */
  async getAllChatFromLocalStorage() {
    try {
      const chatData = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('chatHistory_')) {
          const data = localStorage.getItem(key);
          if (data) {
            chatData[key] = JSON.parse(data);
          }
        }
      }
      return chatData;
    } catch (error) {
      throw new Error('LocalStorage 获取所有聊天记录失败: ' + error.message);
    }
  }

  /**
   * 内存存储获取所有聊天记录
   */
  async getAllChatFromMemory() {
    if (!this.memoryChatStorage) {
      this.memoryChatStorage = {};
    }
    return { ...this.memoryChatStorage };
  }

  /**
   * 批量删除聊天记录
   */
  async removeChatHistoryBatch(storageKeys) {
    await this.init();

    switch (this.storageType) {
      case 'chrome-sync':
      case 'chrome-local':
        return this.removeChatBatchFromChromeLocal(storageKeys);

      case 'localStorage':
        return this.removeChatBatchFromLocalStorage(storageKeys);

      case 'memory':
        return this.removeChatBatchFromMemory(storageKeys);

      default:
        throw new Error('没有可用的存储方案');
    }
  }

  /**
   * Chrome Local 批量删除聊天记录
   */
  async removeChatBatchFromChromeLocal(storageKeys) {
    return new Promise((resolve, reject) => {
      if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
        chrome.storage.local.remove(storageKeys, () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      } else {
        reject(new Error('Chrome Storage Local 不可用'));
      }
    });
  }

  /**
   * LocalStorage 批量删除聊天记录
   */
  async removeChatBatchFromLocalStorage(storageKeys) {
    try {
      storageKeys.forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      throw new Error('LocalStorage 批量删除聊天记录失败: ' + error.message);
    }
  }

  /**
   * 内存存储批量删除聊天记录
   */
  async removeChatBatchFromMemory(storageKeys) {
    if (!this.memoryChatStorage) {
      this.memoryChatStorage = {};
    }
    storageKeys.forEach(key => {
      delete this.memoryChatStorage[key];
    });
  }

  /**
   * 获取存储状态信息
   */
  getStorageInfo() {
    return {
      type: this.storageType,
      isInitialized: this.isInitialized,
      available: this.storageType !== 'memory'
    };
  }
}

// 创建全局实例
window.StorageManager = new StorageManager();
