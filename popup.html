<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      width: 400px;
      height: 600px;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f7fa;
      color: #333;
      overflow: hidden;
    }

    .container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }

    .nav-tabs {
      display: flex;
      gap: 10px;
    }

    .nav-tab {
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 20px;
      color: white;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .nav-tab.active {
      background: rgba(255, 255, 255, 0.3);
      font-weight: 600;
    }

    .nav-tab:hover {
      background: rgba(255, 255, 255, 0.25);
    }

    .content {
      flex: 1;
      overflow: hidden;
      position: relative;
    }

    .page {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 20px;
      overflow-y: auto;
      transition: transform 0.3s ease;
    }

    .page.hidden {
      transform: translateX(100%);
    }

    /* 设置页面样式 */
    .settings-page {
      background: white;
    }

    .input-section {
      margin-bottom: 15px;
    }

    .input-section label {
      display: block;
      margin-bottom: 5px;
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .input-section input, .input-section textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.2s;
    }

    .input-section input:focus, .input-section textarea:focus {
      outline: none;
      border-color: #667eea;
    }

    .input-section textarea {
      height: 80px;
      resize: vertical;
    }

    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }

    button {
      padding: 12px 20px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }

    .primary-btn {
      background: #667eea;
      color: white;
      flex: 1;
    }

    .primary-btn:hover {
      background: #5a6fd8;
    }

    .primary-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .status {
      margin-top: 15px;
      padding: 10px;
      border-radius: 8px;
      font-size: 12px;
      text-align: center;
      display: none;
    }

    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status.loading {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }

    /* 对话页面样式 */
    .chat-page {
      padding: 0;
      background: #f8f9fa;
    }

    .chat-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .page-info {
      background: white;
      padding: 15px 20px;
      border-bottom: 1px solid #e9ecef;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .page-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 5px;
    }

    .page-url {
      font-size: 12px;
      color: #666;
      word-break: break-all;
    }

    .messages-container {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .message {
      max-width: 85%;
      word-wrap: break-word;
    }

    .user-message {
      align-self: flex-end;
    }

    .user-message .message-content {
      background: #667eea;
      color: white;
      padding: 12px 16px;
      border-radius: 18px 18px 4px 18px;
      font-size: 14px;
      line-height: 1.4;
    }

    .ai-message {
      align-self: flex-start;
    }

    .ai-message .message-content {
      background: white;
      color: #333;
      padding: 12px 16px;
      border-radius: 18px 18px 18px 4px;
      font-size: 14px;
      line-height: 1.4;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
    }

    .message-time {
      font-size: 11px;
      color: #999;
      margin-top: 5px;
      text-align: right;
    }

    .ai-message .message-time {
      text-align: left;
    }

    .typing-indicator {
      align-self: flex-start;
      max-width: 85%;
    }

    .typing-indicator .message-content {
      background: white;
      padding: 12px 16px;
      border-radius: 18px 18px 18px 4px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .typing-dots {
      display: flex;
      gap: 3px;
    }

    .typing-dot {
      width: 6px;
      height: 6px;
      background: #999;
      border-radius: 50%;
      animation: typing 1.4s infinite;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typing {
      0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
      }
      30% {
        transform: translateY(-10px);
        opacity: 1;
      }
    }

    .input-container {
      background: white;
      border-top: 1px solid #e9ecef;
      padding: 15px 20px;
    }

    .input-wrapper {
      display: flex;
      gap: 10px;
      align-items: flex-end;
      margin-bottom: 10px;
    }

    #messageInput {
      flex: 1;
      border: 1px solid #ddd;
      border-radius: 20px;
      padding: 10px 15px;
      font-size: 14px;
      resize: none;
      max-height: 100px;
      min-height: 40px;
      font-family: inherit;
    }

    #messageInput:focus {
      outline: none;
      border-color: #667eea;
    }

    .send-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      background: #667eea;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }

    .send-btn:hover:not(:disabled) {
      background: #5a6fd8;
      transform: scale(1.05);
    }

    .send-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .input-actions {
      display: flex;
      gap: 10px;
      justify-content: center;
    }

    .action-btn {
      background: none;
      border: 1px solid #ddd;
      color: #666;
      padding: 6px 12px;
      border-radius: 15px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .action-btn:hover {
      background: #f8f9fa;
      border-color: #667eea;
      color: #667eea;
    }

    .welcome-message {
      text-align: center;
      margin-bottom: 10px;
    }

    /* 滚动条样式 */
    .messages-container::-webkit-scrollbar {
      width: 6px;
    }

    .messages-container::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    /* 响应式调整 */
    @media (max-width: 450px) {
      body {
        width: 350px;
      }
    }

    /* 设置页面特殊样式 */
    .settings-page .input-section:last-of-type {
      margin-bottom: 20px;
    }

    /* 对话页面空状态 */
    .empty-chat {
      text-align: center;
      color: #666;
      font-size: 14px;
      margin: 40px 20px;
    }

    .empty-chat .icon {
      font-size: 48px;
      margin-bottom: 15px;
      opacity: 0.5;
    }

    /* 消息动画 */
    .message {
      animation: messageSlideIn 0.3s ease-out;
    }

    @keyframes messageSlideIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 按钮加载状态 */
    .send-btn.loading {
      pointer-events: none;
    }

    .send-btn.loading svg {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    /* Markdown内容样式 */
    .message-content h1,
    .message-content h2,
    .message-content h3,
    .message-content h4,
    .message-content h5,
    .message-content h6 {
      margin: 10px 0 5px 0;
      font-weight: 600;
    }

    .message-content h1 { font-size: 1.2em; }
    .message-content h2 { font-size: 1.1em; }
    .message-content h3 { font-size: 1.05em; }

    .message-content p {
      margin: 8px 0;
      line-height: 1.5;
    }

    .message-content ul,
    .message-content ol {
      margin: 8px 0;
      padding-left: 20px;
    }

    .message-content li {
      margin: 4px 0;
    }

    .message-content blockquote {
      border-left: 3px solid #667eea;
      margin: 10px 0;
      padding: 5px 0 5px 15px;
      background: rgba(102, 126, 234, 0.1);
      font-style: italic;
    }

    .message-content code {
      background: rgba(0, 0, 0, 0.1);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .message-content pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 5px;
      padding: 10px;
      margin: 10px 0;
      overflow-x: auto;
      font-size: 0.9em;
    }

    .message-content pre code {
      background: none;
      padding: 0;
    }

    .message-content table {
      border-collapse: collapse;
      width: 100%;
      margin: 10px 0;
      font-size: 0.9em;
    }

    .message-content th,
    .message-content td {
      border: 1px solid #ddd;
      padding: 6px 8px;
      text-align: left;
    }

    .message-content th {
      background: #f8f9fa;
      font-weight: 600;
    }

    .message-content a {
      color: #667eea;
      text-decoration: none;
    }

    .message-content a:hover {
      text-decoration: underline;
    }

    .message-content strong {
      font-weight: 600;
    }

    .message-content em {
      font-style: italic;
    }

    /* 流式输出相关样式 */
    .streaming-cursor {
      display: inline-block;
      width: 2px;
      height: 1em;
      background: #667eea;
      animation: blink 1s infinite;
      margin-left: 2px;
    }

    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    .message.streaming .message-content {
      position: relative;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🤖 AI网页助手</h1>
      <div class="nav-tabs">
        <button class="nav-tab active" id="settingsTab">设置</button>
        <button class="nav-tab" id="chatTab">对话</button>
      </div>
    </div>

    <div class="content">
      <!-- 设置页面 -->
      <div class="page settings-page" id="settingsPage">
        <div class="input-section">
          <label for="baseUrl">API Base URL:</label>
          <input type="text" id="baseUrl" placeholder="https://api.openai.com/v1/chat/completions">
        </div>

        <div class="input-section">
          <label for="modelName">模型名称:</label>
          <input type="text" id="modelName" placeholder="gpt-3.5-turbo">
        </div>

        <div class="input-section">
          <label for="apiKey">API密钥:</label>
          <input type="password" id="apiKey" placeholder="输入你的API密钥">
        </div>

        <div class="input-section">
          <label for="systemPrompt">系统提示词:</label>
          <textarea id="systemPrompt" placeholder="设置AI的角色和行为方式..."></textarea>
        </div>

        <div class="button-group">
          <button class="primary-btn" id="saveSettingsBtn">保存设置</button>
        </div>

        <div id="settingsStatus" class="status"></div>
      </div>

      <!-- 对话页面 -->
      <div class="page chat-page hidden" id="chatPage">
        <div class="chat-container">
          <div class="page-info" id="pageInfo">
            <div class="page-title">当前页面: <span id="currentPageTitle">未获取</span></div>
            <div class="page-url" id="currentPageUrl">未获取</div>
          </div>

          <div class="messages-container" id="messagesContainer">
            <!-- 欢迎消息将通过JavaScript动态生成 -->
          </div>

          <div class="input-container">
            <div class="input-wrapper">
              <textarea id="messageInput" placeholder="问我关于这个页面的任何问题..." rows="2"></textarea>
              <button id="sendBtn" class="send-btn" disabled>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                </svg>
              </button>
            </div>
            <div class="input-actions">
              <button id="clearChatBtn" class="action-btn">清空对话</button>
              <button id="refreshPageBtn" class="action-btn">刷新页面信息</button>
              <button id="chatStatsBtn" class="action-btn">聊天统计</button>
              <button id="envCheckBtn" class="action-btn">环境检查</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 存储管理器 -->
  <script src="storage-manager.js"></script>

  <!-- 本地Markdown渲染 -->
  <script src="markdown-renderer.js"></script>

  <script src="popup.js"></script>
</body>
</html>
