<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown渲染测试页面</title>
    <meta name="description" content="测试AI网页助手的Markdown渲染功能是否正常工作">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 4px solid #667eea;
        }
        
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        
        .markdown-content {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
            text-align: center;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-questions {
            background: #e8f5e8;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
        }
        
        .test-questions h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .question-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .question-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #4caf50;
            font-style: italic;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 Markdown渲染测试</h1>
            <p>测试AI助手的Markdown格式渲染功能</p>
        </div>
        
        <div class="highlight-box">
            <h2>🎯 测试目标</h2>
            <p>验证AI助手能够正确渲染各种Markdown格式，包括标题、列表、代码块、表格等。</p>
        </div>
        
        <div class="test-section">
            <h3>📋 Markdown格式测试内容</h3>
            <p>以下是一些包含丰富Markdown格式的示例内容，你可以要求AI生成类似的回复来测试渲染效果：</p>
            
            <div class="markdown-content">
                <h4>示例1: 基本格式</h4>
                <p><strong>粗体文本</strong> 和 <em>斜体文本</em></p>
                <p><code>内联代码</code> 和 <del>删除线文本</del></p>
                <p><a href="#" target="_blank">链接示例</a></p>
            </div>
            
            <div class="markdown-content">
                <h4>示例2: 列表</h4>
                <ul>
                    <li>无序列表项目1</li>
                    <li>无序列表项目2</li>
                    <li>无序列表项目3</li>
                </ul>
                <ol>
                    <li>有序列表项目1</li>
                    <li>有序列表项目2</li>
                    <li>有序列表项目3</li>
                </ol>
            </div>
            
            <div class="markdown-content">
                <h4>示例3: 代码块</h4>
                <pre><code class="language-javascript">function greet(name) {
  return `Hello, ${name}!`;
}

const message = greet('World');
console.log(message);</code></pre>
            </div>
            
            <div class="markdown-content">
                <h4>示例4: 表格</h4>
                <table>
                    <tr><th>功能</th><th>状态</th><th>说明</th></tr>
                    <tr><td>标题渲染</td><td>✅</td><td>支持H1-H6</td></tr>
                    <tr><td>列表渲染</td><td>✅</td><td>有序和无序</td></tr>
                    <tr><td>代码高亮</td><td>✅</td><td>JavaScript等</td></tr>
                </table>
            </div>
            
            <div class="markdown-content">
                <h4>示例5: 引用块</h4>
                <blockquote>这是一个引用块的示例，用于展示重要的引用内容。</blockquote>
            </div>
        </div>
        
        <div class="test-questions">
            <h3>🧪 建议测试问题</h3>
            <p>尝试问AI这些问题来测试Markdown渲染：</p>
            <div class="question-list">
                <div class="question-item">
                    "用Markdown格式创建一个包含标题、列表和代码块的技术文档"
                </div>
                <div class="question-item">
                    "制作一个表格对比不同编程语言的特点"
                </div>
                <div class="question-item">
                    "写一段JavaScript代码并用Markdown格式展示"
                </div>
                <div class="question-item">
                    "用Markdown格式总结这个页面的主要内容，包括标题和列表"
                </div>
                <div class="question-item">
                    "创建一个包含粗体、斜体、代码的格式化回复"
                </div>
                <div class="question-item">
                    "用引用块的形式给出一些重要提示"
                </div>
            </div>
        </div>
        
        <div class="success">
            <strong>✅ 测试步骤：</strong>
            <ol>
                <li>打开AI助手插件</li>
                <li>配置API设置</li>
                <li>切换到对话页面</li>
                <li>尝试上述测试问题</li>
                <li>观察AI回复的Markdown渲染效果</li>
            </ol>
        </div>
        
        <div class="warning">
            <strong>⚠️ 注意事项：</strong>
            <ul>
                <li>确保插件已正确加载markdown-renderer.js文件</li>
                <li>检查浏览器控制台是否有错误信息</li>
                <li>如果渲染异常，请刷新页面重试</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 技术说明</h3>
            <p>本版本使用自定义的Markdown渲染器，支持以下功能：</p>
            <ul>
                <li><strong>标题</strong>: H1-H6 (#, ##, ###, 等)</li>
                <li><strong>文本格式</strong>: 粗体(**text**), 斜体(*text*), 删除线(~~text~~)</li>
                <li><strong>代码</strong>: 内联代码(`code`), 代码块(```)</li>
                <li><strong>列表</strong>: 有序列表(1. 2. 3.), 无序列表(- * +)</li>
                <li><strong>链接</strong>: [文本](URL)</li>
                <li><strong>表格</strong>: | 列1 | 列2 | 格式</li>
                <li><strong>引用</strong>: > 引用文本</li>
                <li><strong>代码高亮</strong>: JavaScript关键字和语法高亮</li>
            </ul>
        </div>
        
        <div class="highlight-box">
            <h3>🚀 开始测试</h3>
            <p>现在点击浏览器工具栏中的AI助手图标，开始测试Markdown渲染功能！</p>
        </div>
    </div>
</body>
</html>
