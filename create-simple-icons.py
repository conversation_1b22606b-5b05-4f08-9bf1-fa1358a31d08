#!/usr/bin/env python3
"""
简单的图标生成脚本
需要安装 Pillow: pip install Pillow
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_icon(size):
        # 创建图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制圆角矩形背景
        margin = size // 8
        draw.rounded_rectangle(
            [margin, margin, size - margin, size - margin],
            radius=size // 6,
            fill=(102, 126, 234, 255)  # #667eea
        )
        
        # 绘制简单的机器人图标
        center = size // 2
        
        # 机器人头部
        head_size = size // 3
        head_x = center - head_size // 2
        head_y = center - head_size // 2
        draw.rounded_rectangle(
            [head_x, head_y, head_x + head_size, head_y + head_size],
            radius=head_size // 6,
            fill=(255, 255, 255, 255)
        )
        
        # 眼睛
        eye_size = size // 16
        eye_y = center - size // 8
        draw.ellipse([center - size // 6, eye_y, center - size // 6 + eye_size, eye_y + eye_size], fill=(102, 126, 234, 255))
        draw.ellipse([center + size // 6 - eye_size, eye_y, center + size // 6, eye_y + eye_size], fill=(102, 126, 234, 255))
        
        # 嘴巴
        mouth_y = center + size // 12
        mouth_width = size // 8
        draw.rectangle([center - mouth_width // 2, mouth_y, center + mouth_width // 2, mouth_y + 2], fill=(102, 126, 234, 255))
        
        return img
    
    # 创建icons目录
    icons_dir = 'icons'
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    # 生成不同尺寸的图标
    sizes = [16, 48, 128]
    
    for size in sizes:
        img = create_icon(size)
        filename = os.path.join(icons_dir, f'icon{size}.png')
        img.save(filename, 'PNG')
        print(f'Created {filename}')
    
    print('所有图标已生成完成！')
    print('现在可以在manifest.json中重新添加图标引用了。')
    
except ImportError:
    print('Pillow包未安装，请使用以下命令安装：')
    print('pip install Pillow')
    print('')
    print('或者使用其他方法创建图标：')
    print('1. 打开 create-icons.html 文件按照说明操作')
    print('2. 使用在线图标生成器')
    print('3. 暂时不使用图标（插件仍可正常工作）')
except Exception as e:
    print(f'生成图标时出错: {e}')
    print('请尝试其他方法创建图标文件。')
