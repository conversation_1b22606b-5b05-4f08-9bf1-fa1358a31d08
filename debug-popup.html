<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手调试页面</title>
    <style>
        body {
            width: 400px;
            height: 600px;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .debug-section {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        input, textarea, button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            padding: 10px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        #debugLog {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="debug-section">
        <h3>🔧 AI助手调试工具</h3>
        <p>用于测试插件的基本功能</p>
    </div>
    
    <div class="debug-section">
        <h3>⚙️ 设置测试</h3>
        <input type="text" id="baseUrl" placeholder="API Base URL" value="https://api.openai.com/v1/chat/completions">
        <input type="text" id="modelName" placeholder="模型名称" value="gpt-3.5-turbo">
        <input type="password" id="apiKey" placeholder="API密钥">
        <textarea id="systemPrompt" placeholder="系统提示词" rows="3">你是一个智能网页助手，能够分析网页内容并回答用户的问题。</textarea>
        <button id="saveSettingsBtn">保存设置</button>
        <div id="settingsStatus" class="status"></div>
    </div>
    
    <div class="debug-section">
        <h3>🔍 存储测试</h3>
        <button id="testStorageBtn">测试Chrome存储</button>
        <button id="loadSettingsBtn">加载设置</button>
        <button id="clearStorageBtn">清空存储</button>
    </div>
    
    <div class="debug-section">
        <h3>📋 调试日志</h3>
        <div id="debugLog"></div>
        <button id="clearLogBtn">清空日志</button>
    </div>
    
    <script>
        // 调试日志功能
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // 显示状态消息
        function showStatus(message, type) {
            const status = document.getElementById('settingsStatus');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            log(`状态: ${message} (${type})`);
            
            if (type !== 'loading') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
        }
        
        // 保存设置
        function saveSettings() {
            log('开始保存设置...');
            
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const modelName = document.getElementById('modelName').value.trim();
            const apiKey = document.getElementById('apiKey').value.trim();
            const systemPrompt = document.getElementById('systemPrompt').value.trim();
            
            log(`设置值: baseUrl=${baseUrl}, modelName=${modelName}, apiKey=${apiKey ? '***' : ''}`);
            
            if (!baseUrl || !modelName || !apiKey) {
                showStatus('请填写所有必填字段', 'error');
                return;
            }
            
            showStatus('正在保存...', 'loading');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.set({
                    baseUrl: baseUrl,
                    modelName: modelName,
                    apiKey: apiKey,
                    systemPrompt: systemPrompt
                }, function() {
                    if (chrome.runtime.lastError) {
                        log('保存失败: ' + chrome.runtime.lastError.message);
                        showStatus('保存失败: ' + chrome.runtime.lastError.message, 'error');
                    } else {
                        log('保存成功');
                        showStatus('设置保存成功！', 'success');
                    }
                });
            } else {
                log('Chrome存储API不可用');
                showStatus('Chrome存储API不可用', 'error');
            }
        }
        
        // 加载设置
        function loadSettings() {
            log('开始加载设置...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.get(['baseUrl', 'modelName', 'apiKey', 'systemPrompt'], function(result) {
                    if (chrome.runtime.lastError) {
                        log('加载失败: ' + chrome.runtime.lastError.message);
                        showStatus('加载失败', 'error');
                    } else {
                        log('加载成功: ' + JSON.stringify(result));
                        
                        if (result.baseUrl) document.getElementById('baseUrl').value = result.baseUrl;
                        if (result.modelName) document.getElementById('modelName').value = result.modelName;
                        if (result.apiKey) document.getElementById('apiKey').value = result.apiKey;
                        if (result.systemPrompt) document.getElementById('systemPrompt').value = result.systemPrompt;
                        
                        showStatus('设置加载成功', 'success');
                    }
                });
            } else {
                log('Chrome存储API不可用');
                showStatus('Chrome存储API不可用', 'error');
            }
        }
        
        // 测试存储
        function testStorage() {
            log('测试Chrome存储...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const testData = { test: 'Hello World', timestamp: Date.now() };
                
                chrome.storage.sync.set(testData, function() {
                    if (chrome.runtime.lastError) {
                        log('存储测试失败: ' + chrome.runtime.lastError.message);
                        showStatus('存储测试失败', 'error');
                    } else {
                        log('存储测试成功');
                        
                        chrome.storage.sync.get(['test', 'timestamp'], function(result) {
                            if (chrome.runtime.lastError) {
                                log('读取测试失败: ' + chrome.runtime.lastError.message);
                            } else {
                                log('读取测试成功: ' + JSON.stringify(result));
                                showStatus('存储功能正常', 'success');
                            }
                        });
                    }
                });
            } else {
                log('Chrome存储API不可用');
                showStatus('Chrome存储API不可用', 'error');
            }
        }
        
        // 清空存储
        function clearStorage() {
            log('清空存储...');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.clear(function() {
                    if (chrome.runtime.lastError) {
                        log('清空失败: ' + chrome.runtime.lastError.message);
                        showStatus('清空失败', 'error');
                    } else {
                        log('存储已清空');
                        showStatus('存储已清空', 'success');
                        
                        // 清空表单
                        document.getElementById('baseUrl').value = 'https://api.openai.com/v1/chat/completions';
                        document.getElementById('modelName').value = 'gpt-3.5-turbo';
                        document.getElementById('apiKey').value = '';
                        document.getElementById('systemPrompt').value = '你是一个智能网页助手，能够分析网页内容并回答用户的问题。';
                    }
                });
            } else {
                log('Chrome存储API不可用');
                showStatus('Chrome存储API不可用', 'error');
            }
        }
        
        // 环境检查
        function checkEnvironment() {
            log('=== 环境检查开始 ===');

            // 检查是否在扩展环境中
            log('当前URL: ' + window.location.href);
            log('User Agent: ' + navigator.userAgent);

            // 检查Chrome对象
            if (typeof chrome === 'undefined') {
                log('❌ chrome对象不存在');
                return false;
            } else {
                log('✅ chrome对象存在');
            }

            // 检查chrome.storage
            if (typeof chrome.storage === 'undefined') {
                log('❌ chrome.storage不存在');
                return false;
            } else {
                log('✅ chrome.storage存在');
            }

            // 检查chrome.storage.sync
            if (typeof chrome.storage.sync === 'undefined') {
                log('❌ chrome.storage.sync不存在');
                return false;
            } else {
                log('✅ chrome.storage.sync存在');
            }

            // 检查chrome.runtime
            if (typeof chrome.runtime === 'undefined') {
                log('❌ chrome.runtime不存在');
                return false;
            } else {
                log('✅ chrome.runtime存在');
                log('扩展ID: ' + chrome.runtime.id);
            }

            log('=== 环境检查完成 ===');
            return true;
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面已加载');

            // 先进行环境检查
            const envOk = checkEnvironment();
            if (!envOk) {
                log('⚠️ 环境检查失败，某些功能可能不可用');
            }

            document.getElementById('saveSettingsBtn').addEventListener('click', saveSettings);
            document.getElementById('testStorageBtn').addEventListener('click', testStorage);
            document.getElementById('loadSettingsBtn').addEventListener('click', loadSettings);
            document.getElementById('clearStorageBtn').addEventListener('click', clearStorage);
            document.getElementById('clearLogBtn').addEventListener('click', function() {
                document.getElementById('debugLog').textContent = '';
            });

            log('事件监听器已设置');

            // 自动加载设置
            setTimeout(loadSettings, 100);
        });
    </script>
</body>
</html>
