# 更新日志

## v2.1.0 - SSE流式输出和Markdown支持 (2024-01-XX)

### 🎉 新增功能
- **SSE流式输出**: 支持Server-Sent Events实时响应，提供自然的对话体验
- **Markdown渲染**: 完整支持Markdown语法，包括标题、列表、代码块、表格等
- **代码高亮**: 集成highlight.js，支持多种编程语言的语法高亮
- **流式动画**: 打字光标动画和实时内容更新效果

### 🔧 技术改进
- 重构API调用逻辑，支持流式数据处理
- 集成marked.js和highlight.js库
- 优化消息渲染性能
- 增强错误处理和用户反馈

### 📚 文档和测试
- 新增`sse-markdown-test.html`测试页面
- 更新README.md和INSTALL.md文档
- 添加详细的使用示例和配置说明

### 🐛 修复问题
- 修复流式输出中的数据解析问题
- 优化Markdown渲染的性能
- 改进错误提示的准确性

---

## v2.0.0 - 对话界面重构 (2024-01-XX)

### 🎉 新增功能
- **对话界面**: 重新设计为聊天式交互
- **连续对话**: 支持多轮问答和上下文记忆
- **双页面设计**: 分离的设置页面和对话页面
- **页面上下文**: AI能够理解当前页面内容

### ⚙️ 配置增强
- 支持多种AI服务提供商(OpenAI, Azure OpenAI等)
- 可配置API Base URL和模型名称
- 自定义系统提示词
- 设置持久化存储

### 🎨 界面优化
- 现代化的聊天界面设计
- 消息气泡和时间戳
- 响应式设计
- 流畅的页面切换动画

### 🔧 技术改进
- 重构popup.js架构
- 改进content script注入机制
- 增强错误处理和调试功能
- 优化性能和内存使用

### 📚 文档和测试
- 新增`chat-test.html`和`debug-test.html`测试页面
- 完善安装和使用文档
- 添加故障排除指南

---

## v1.0.0 - 初始版本 (2024-01-XX)

### 🎉 首次发布
- 基本的AI网页分析功能
- OpenAI GPT API集成
- 自定义提示词支持
- 现代化弹窗界面

### 核心功能
- 页面内容提取和分析
- AI生成的智能弹窗
- 用户配置保存
- Chrome Extension Manifest V3支持

### 技术特性
- 基于Chrome Extension API
- 现代JavaScript(ES6+)
- CSS3动画和样式
- 本地数据存储

---

## 开发计划

### 未来版本规划
- [ ] 支持更多AI服务提供商
- [ ] 添加语音输入/输出功能
- [ ] 支持图片分析
- [ ] 多语言界面支持
- [ ] 导出对话历史
- [ ] 自定义主题和样式
- [ ] 插件设置同步

### 已知问题
- 某些网站的CSP策略可能影响插件功能
- 流式输出在网络不稳定时可能中断
- 大量Markdown内容可能影响渲染性能

### 贡献指南
欢迎提交Issue和Pull Request！请确保：
1. 遵循现有的代码风格
2. 添加适当的测试
3. 更新相关文档
4. 详细描述变更内容
