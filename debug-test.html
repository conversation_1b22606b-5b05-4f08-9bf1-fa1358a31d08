<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手调试测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        #log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🤖 AI网页助手调试测试页面</h1>
    
    <div class="debug-info">
        <h2>调试信息</h2>
        <p><strong>页面URL:</strong> <span id="pageUrl"></span></p>
        <p><strong>页面标题:</strong> <span id="pageTitle"></span></p>
        <p><strong>时间:</strong> <span id="currentTime"></span></p>
    </div>

    <h2>测试步骤</h2>
    <ol>
        <li>确保Chrome插件已经加载</li>
        <li>打开浏览器开发者工具 (F12)</li>
        <li>点击插件图标，配置API设置</li>
        <li>点击"分析页面"按钮</li>
        <li>查看控制台日志和下方的日志输出</li>
    </ol>

    <div>
        <button class="test-button" onclick="testContentScript()">测试Content Script连接</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>

    <h3>日志输出:</h3>
    <div id="log"></div>

    <h2>页面内容</h2>
    <p>这是一个简单的测试页面，用于验证AI网页助手的功能。页面包含了基本的HTML结构和一些测试内容。</p>
    
    <h3>测试内容区域</h3>
    <p>AI助手应该能够分析这个页面的内容，包括标题、文本内容和页面结构。这里有一些示例文本供AI分析：</p>
    
    <ul>
        <li>这是一个技术测试页面</li>
        <li>包含了调试功能</li>
        <li>用于验证Chrome插件的工作状态</li>
        <li>支持实时日志输出</li>
    </ul>

    <div class="debug-info">
        <h4>技术信息</h4>
        <p>用户代理: <span id="userAgent"></span></p>
        <p>屏幕分辨率: <span id="screenRes"></span></p>
    </div>

    <script>
        // 更新页面信息
        document.getElementById('pageUrl').textContent = window.location.href;
        document.getElementById('pageTitle').textContent = document.title;
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('screenRes').textContent = screen.width + 'x' + screen.height;

        // 日志功能
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 测试Content Script连接
        function testContentScript() {
            log('开始测试Content Script连接...');
            
            // 检查是否有AI助手实例
            if (typeof aiAssistant !== 'undefined') {
                log('✓ AI助手实例已找到');
            } else {
                log('✗ AI助手实例未找到');
            }

            // 检查Chrome扩展API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('✓ Chrome扩展API可用');
            } else {
                log('✗ Chrome扩展API不可用');
            }
        }

        // 页面加载完成时的初始化
        window.addEventListener('load', function() {
            log('页面加载完成');
            log('开始检查AI助手状态...');
            
            setTimeout(() => {
                if (typeof aiAssistant !== 'undefined') {
                    log('✓ AI助手已成功加载');
                } else {
                    log('✗ AI助手未加载，请检查插件状态');
                }
            }, 1000);
        });

        // 监听可能的错误
        window.addEventListener('error', function(e) {
            log(`错误: ${e.message} (${e.filename}:${e.lineno})`);
        });
    </script>
</body>
</html>
